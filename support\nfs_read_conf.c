/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright CEA/DAM/DIF  (2008)
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * ---------------------------------------
 */

/**
 * @file  nfs_read_conf.c
 * @brief This file tables required for parsing the NFS specific parameters.
 */

#include "config.h"
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <fcntl.h>
#include <sys/file.h>		/* for having FNDELAY */
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <ctype.h>
#include "log.h"
#include "gsh_rpc.h"
#include "fsal.h"
#include "nfs23.h"
#include "nfs4.h"
#include "mount.h"
#include "nfs_core.h"
#include "nfs_file_handle.h"
#include "nfs_exports.h"
#include "nfs_proto_functions.h"
#include "nfs_dupreq.h"
#include "config_parsing.h"

/**
 * @brief Core configuration parameters
 */

static struct config_item_list udp_listener_type[] = {
	CONFIG_LIST_TOK("false", UDP_LISTENER_NONE),
	CONFIG_LIST_TOK("no", UDP_LISTENER_NONE),
	CONFIG_LIST_TOK("off", UDP_LISTENER_NONE),
	CONFIG_LIST_TOK("true", UDP_LISTENER_ALL),
	CONFIG_LIST_TOK("yes", UDP_LISTENER_ALL),
	CONFIG_LIST_TOK("on", UDP_LISTENER_ALL),
	CONFIG_LIST_TOK("mount", UDP_LISTENER_MOUNT),
	CONFIG_LIST_EOL
};

static struct config_item_list protocols[] = {
	CONFIG_LIST_TOK("none", CORE_OPTION_NONE),
#ifdef _USE_NFS3
	CONFIG_LIST_TOK("3", CORE_OPTION_NFSV3),
	CONFIG_LIST_TOK("v3", CORE_OPTION_NFSV3),
	CONFIG_LIST_TOK("nfs3", CORE_OPTION_NFSV3),
	CONFIG_LIST_TOK("nfsv3", CORE_OPTION_NFSV3),
#endif
	CONFIG_LIST_TOK("4", CORE_OPTION_NFSV4),
	CONFIG_LIST_TOK("v4", CORE_OPTION_NFSV4),
	CONFIG_LIST_TOK("nfs4", CORE_OPTION_NFSV4),
	CONFIG_LIST_TOK("nfsv4", CORE_OPTION_NFSV4),
#ifdef RPC_VSOCK
	CONFIG_LIST_TOK("nfsvsock", CORE_OPTION_NFS_VSOCK),
#endif
#ifdef _USE_NFS_RDMA
	CONFIG_LIST_TOK("nfsrdma", CORE_OPTION_NFS_RDMA),
	CONFIG_LIST_TOK("rpcrdma", CORE_OPTION_NFS_RDMA),
#endif
#ifdef _USE_9P
	CONFIG_LIST_TOK("9p", CORE_OPTION_9P),
#endif
	CONFIG_LIST_EOL
};

/**
 * @brief Support all protocols
 */
#ifdef _USE_NFS3
#define DEFAULT_INCLUDES_NFSV3		CORE_OPTION_NFSV3
#else
#define DEFAULT_INCLUDES_NFSV3		CORE_OPTION_NONE
#endif

#define DEFAULT_INCLUDES_NFSV4		CORE_OPTION_NFSV4

#define DEFAULT_PROTOCOLS  (DEFAULT_INCLUDES_NFSV3 | \
			    DEFAULT_INCLUDES_NFSV4)

static struct config_item core_params[] = {
	CONF_ITEM_UI16("NFS_Port", 0, UINT16_MAX, NFS_PORT,
		       nfs_core_param, port[P_NFS]),
#ifdef _USE_NFS3
	CONF_ITEM_UI16("MNT_Port", 0, UINT16_MAX, MNT_PORT,
		       nfs_core_param, port[P_MNT]),
#endif
#ifdef _USE_NLM
	CONF_ITEM_UI16("NLM_Port", 0, UINT16_MAX, NLM_PORT,
		       nfs_core_param, port[P_NLM]),
#endif
#ifdef _USE_RQUOTA
	CONF_ITEM_UI16("Rquota_Port", 0, UINT16_MAX, RQUOTA_PORT,
		       nfs_core_param, port[P_RQUOTA]),
#endif
	CONF_ITEM_IP_ADDR("Bind_Addr", "0.0.0.0",
			  nfs_core_param, bind_addr),
	CONF_ITEM_UI32("NFS_Program", 1, INT32_MAX, NFS_PROGRAM,
		       nfs_core_param, program[P_NFS]),
#ifdef _USE_NFS3
	CONF_ITEM_UI32("MNT_Program", 1, INT32_MAX, MOUNTPROG,
				nfs_core_param, program[P_MNT]),
#endif
#ifdef _USE_NLM
	CONF_ITEM_UI32("NLM_Program", 1, INT32_MAX, NLMPROG,
		       nfs_core_param, program[P_NLM]),
#endif
#ifdef _USE_RQUOTA
	CONF_ITEM_UI32("Rquota_Program", 1, INT32_MAX, RQUOTAPROG,
		       nfs_core_param, program[P_RQUOTA]),
#endif
#ifdef USE_NFSACL3
	CONF_ITEM_UI32("NFSACL_Program", 1, INT32_MAX, NFSACLPROG,
		       nfs_core_param, program[P_NFSACL]),
#endif
	CONF_ITEM_DEPRECATED("Nb_Worker",
			     "This parameter has been replaced with _9P { Nb_Worker}"
			     ),
	CONF_ITEM_BOOL("Drop_IO_Errors", false,
		       nfs_core_param, drop_io_errors),
	CONF_ITEM_BOOL("Drop_Inval_Errors", false,
		       nfs_core_param, drop_inval_errors),
	CONF_ITEM_BOOL("Drop_Delay_Errors", false,
		       nfs_core_param, drop_delay_errors),
	CONF_ITEM_BOOL("DRC_Disabled", false,
		       nfs_core_param, drc.disabled),
	CONF_ITEM_UI32("DRC_TCP_Npart", 1, 20, DRC_TCP_NPART,
		       nfs_core_param, drc.tcp.npart),
	CONF_ITEM_UI32("DRC_TCP_Size", 1, 32767, DRC_TCP_SIZE,
		       nfs_core_param, drc.tcp.size),
	CONF_ITEM_UI32("DRC_TCP_Cachesz", 1, 255, DRC_TCP_CACHESZ,
		       nfs_core_param, drc.tcp.cachesz),
	CONF_ITEM_UI32("DRC_TCP_Hiwat", 1, 256, DRC_TCP_HIWAT,
		       nfs_core_param, drc.tcp.hiwat),
	CONF_ITEM_UI32("DRC_TCP_Recycle_Npart", 1, 20, DRC_TCP_RECYCLE_NPART,
		       nfs_core_param, drc.tcp.recycle_npart),
	CONF_ITEM_UI32("DRC_TCP_Recycle_Expire_S", 0, 60*60, 600,
		       nfs_core_param, drc.tcp.recycle_expire_s),
	CONF_ITEM_BOOL("DRC_TCP_Checksum", DRC_TCP_CHECKSUM,
		       nfs_core_param, drc.tcp.checksum),
	CONF_ITEM_UI32("DRC_UDP_Npart", 1, 100, DRC_UDP_NPART,
		       nfs_core_param, drc.udp.npart),
	CONF_ITEM_UI32("DRC_UDP_Size", 512, 32768, DRC_UDP_SIZE,
		       nfs_core_param, drc.udp.size),
	CONF_ITEM_UI32("DRC_UDP_Cachesz", 1, 2047, DRC_UDP_CACHESZ,
		       nfs_core_param, drc.udp.cachesz),
	CONF_ITEM_UI32("DRC_UDP_Hiwat", 1, 32768, DRC_UDP_HIWAT,
		       nfs_core_param, drc.udp.hiwat),
	CONF_ITEM_BOOL("DRC_UDP_Checksum", DRC_UDP_CHECKSUM,
		       nfs_core_param, drc.udp.checksum),
	CONF_ITEM_UI32("RPC_Max_Connections", 1, 10000, 3000,
		       nfs_core_param, rpc.max_connections),
	CONF_ITEM_UI32("RPC_Idle_Timeout_S", 0, 60*60, 300,
		       nfs_core_param, rpc.idle_timeout_s),
	CONF_ITEM_UI32("MaxRPCSendBufferSize", 1, 1048576*9,
		       NFS_DEFAULT_SEND_BUFFER_SIZE,
		       nfs_core_param, rpc.max_send_buffer_size),
	CONF_ITEM_UI32("MaxRPCRecvBufferSize", 1, 1048576*9,
		       NFS_DEFAULT_RECV_BUFFER_SIZE,
		       nfs_core_param, rpc.max_recv_buffer_size),
	CONF_ITEM_UI32("rpc_ioq_thrdmin", 2, 1024*128, 2,
		       nfs_core_param, rpc.ioq_thrd_min),
	CONF_ITEM_UI32("RPC_Ioq_ThrdMax", 2, 1024*128, 200,
		       nfs_core_param, rpc.ioq_thrd_max),
	CONF_ITEM_UI32("RPC_GSS_Npart", 1, 1021, 13,
		       nfs_core_param, rpc.gss.ctx_hash_partitions),
	CONF_ITEM_UI32("RPC_GSS_Max_Ctx", 1, 1024*1024, 16384,
		       nfs_core_param, rpc.gss.max_ctx),
	CONF_ITEM_UI32("RPC_GSS_Max_GC", 1, 1024*1024, 200,
		       nfs_core_param, rpc.gss.max_gc),
	CONF_ITEM_I64("Blocked_Lock_Poller_Interval", 0, 180, 10,
		      nfs_core_param, blocked_lock_poller_interval),
	CONF_ITEM_LIST("NFS_Protocols", DEFAULT_PROTOCOLS, protocols,
		       nfs_core_param, core_options),
	CONF_ITEM_LIST("Protocols", DEFAULT_PROTOCOLS, protocols,
		       nfs_core_param, core_options),
	CONF_ITEM_BOOL("Clustered", true,
		       nfs_core_param, clustered),
	CONF_ITEM_BOOL("Show_Vip", true,
		       nfs_core_param, show_vip),
#ifdef _USE_NLM
	CONF_ITEM_BOOL("Enable_NLM", true,
		       nfs_core_param, enable_NLM),
	CONF_ITEM_BOOL("NSM_Use_Caller_Name", false,
		       nfs_core_param, nsm_use_caller_name),
#endif
#ifdef _USE_RQUOTA
	CONF_ITEM_BOOL("Enable_RQUOTA", true,
		       nfs_core_param, enable_RQUOTA),
#endif
#ifdef USE_NFSACL3
	CONF_ITEM_BOOL("Enable_NFSACL", false,
		       nfs_core_param, enable_NFSACL),
#endif
	CONF_ITEM_BOOL("Enable_TCP_keepalive", true,
		       nfs_core_param, enable_tcp_keepalive),
	CONF_ITEM_UI32("TCP_KEEPCNT", 0, 255, 0,
		       nfs_core_param, tcp_keepcnt),
	CONF_ITEM_UI32("TCP_KEEPIDLE", 0, 65535, 0,
		       nfs_core_param, tcp_keepidle),
	CONF_ITEM_UI32("TCP_KEEPINTVL", 0, 65535, 0,
		       nfs_core_param, tcp_keepintvl),
	CONF_ITEM_BOOL("Enable_NFS_Stats", true,
		       nfs_core_param, enable_NFSSTATS),
	CONF_ITEM_BOOL("Enable_Fast_Stats", false,
		       nfs_core_param, enable_FASTSTATS),
	CONF_ITEM_BOOL("Enable_FSAL_Stats", false,
		       nfs_core_param, enable_FSALSTATS),
#ifdef _USE_NFS3
	CONF_ITEM_BOOL("Enable_FULLV3_Stats", false,
		       nfs_core_param, enable_FULLV3STATS),
#endif
	CONF_ITEM_BOOL("Enable_FULLV4_Stats", false,
		       nfs_core_param, enable_FULLV4STATS),
	CONF_ITEM_BOOL("Enable_AUTH_Stats", false,
		       nfs_core_param, enable_AUTHSTATS),
	CONF_ITEM_BOOL("Enable_CLNT_AllOps_Stats", true,
		       nfs_core_param, enable_CLNTALLSTATS),
	CONF_ITEM_BOOL("Short_File_Handle", false,
		       nfs_core_param, short_file_handle),
	CONF_ITEM_I64("Manage_Gids_Expiration", 0, 7*24*60*60, 30*60,
			nfs_core_param, manage_gids_expiration),
	CONF_ITEM_PATH("Plugins_Dir", 1, MAXPATHLEN, FSAL_MODULE_LOC,
		       nfs_core_param, gnfs_modules_loc),
	CONF_ITEM_UI32("heartbeat_freq", 0, 5000, 1000,
		       nfs_core_param, heartbeat_freq),
	CONF_ITEM_BOOL("fsid_device", false,
		       nfs_core_param, fsid_device),
	CONF_ITEM_BOOL("mount_path_pseudo", false,
		       nfs_core_param, mount_path_pseudo),
	CONF_ITEM_ENUM_BITS("Enable_UDP", UDP_LISTENER_ALL, UDP_LISTENER_MASK,
		       udp_listener_type, nfs_core_param, enable_UDP),
	CONF_ITEM_STR("Dbus_Name_Prefix", 1, 255, NULL,
		       nfs_core_param, dbus_name_prefix),
	CONF_ITEM_UI32("Max_Uid_To_Group_Reqs", 0, INT32_MAX, 0,
		       nfs_core_param, max_uid_to_grp_reqs),
	CONF_ITEM_BOOL("Enable_V3fh_Validation_For_V4", false,
		       nfs_core_param, enable_v3_fh_for_v4),
	/*add by zhanghao at 2021.4.20 for performance opt*/
	CONF_ITEM_BOOL("Performance_Opt", false,
		       nfs_core_param, performance_OPT),
	CONF_ITEM_BOOL("Enable_RPC_Stats", false,
			   nfs_core_param, enable_RPCSTATS),
	/*add by zhangjiali at 2021.5.24 for threads stats*/
	CONF_ITEM_BOOL("Enable_THR_Stats", false,
			   nfs_core_param, enable_THRSTATS),
	/*add by zhangjiali at 2021.5.12 for rpc write zero cpy*/
	CONF_ITEM_BOOL("Enable_Write_ZeroCpy", true,
		       nfs_core_param, enable_write_ZEROCPY),
	/*add by zhangjiali at 2021.5.20 for zerocpy read */
	CONF_ITEM_BOOL("Enable_Read_ZeroCpy", true,
		       nfs_core_param, enable_read_ZEROCPY),
	/*add by zhangjiali for zerocpy read test multi iovs*/
	CONF_ITEM_UI32("Test_Readv_iovs", 0, 255, 0,
		       nfs_core_param, test_readv_iovs),
	/*add by zhangjiali for multi-gnfs*/
	CONF_ITEM_BOOL("Enable_Multi_NFS", false,
		 	nfs_core_param, enable_multi_NFS),
	/*add by zhangjiali for set reclaim session timeout  
	 *  -1:disable idfs msd session timout , 0: idfs msd always waiting */
	CONF_ITEM_I32("IDFS_Session_Timeout", -1, 5000, -1,
			nfs_core_param, idfs_session_timeout),
	/*add by zhangjiali for mempool*/
	CONF_ITEM_BOOL("Enable_Memory_Pool", false,
			   nfs_core_param, enable_MEMPOOL),
	/*add by zhangjiali for memstat*/
	CONF_ITEM_BOOL("Enable_Memory_Stat", false,
			   nfs_core_param, enable_MEMSTAT),
	/*add by zhangjiali for mempool size 2*1000 = 2G*/
	CONF_ITEM_UI64("Memory_Pool_Size", 0, 20*1000, 2*1000,
		       nfs_core_param, mempool_SIZE),
	/*add by zhangjiali for loopback test*/
	CONF_ITEM_BOOL("Enable_Write_Loopback", false,
			   nfs_core_param, enable_write_LOOPBACK),
	CONF_ITEM_BOOL("Enable_Read_Loopback", false,
			   nfs_core_param, enable_read_LOOPBACK),
	CONF_ITEM_BOOL("Enable_Access_Loopback", false,
			   nfs_core_param, enable_access_LOOPBACK),
	CONF_ITEM_BOOL("Enable_Getattr_Loopback", false,
			   nfs_core_param, enable_getattr_LOOPBACK),
	CONF_ITEM_UI64("Loopback_For_size", 0, 1048576, 0,
		       nfs_core_param, loopback_for_size),
	/*add by zhangjiali for Write loopback delay (us) 1000=1ms*/
	CONF_ITEM_UI64("Write_Loopback_Delay", 0, 20000*1000, 0,
		       nfs_core_param, write_loopback_delay),
	/*add by zhangjiali for Read loopback delay (us) 1000=1ms*/
	CONF_ITEM_UI64("Read_Loopback_Delay", 0, 20000*1000, 0,
		       nfs_core_param, read_loopback_delay),
	/*add by zhangjiali for commit  delay (us) 1000=1ms*/
	CONF_ITEM_UI64("Commit_Delay", 0, 20000*1000, 0,
		       nfs_core_param, commit_delay),
	/*add by zhangjiali for io stat*/
	CONF_ITEM_BOOL("Enable_IO_Stats", false,
			nfs_core_param, enable_IO_STATS),
	CONF_ITEM_BOOL("Enable_Root_Export", true,
			nfs_core_param, enable_root_export),	
	CONF_ITEM_BOOL("Enable_To_Get_Inherit_Acl", false,
			nfs_core_param, enable_to_get_inherit_acl),	
	CONF_ITEM_BOOL("Access_User_Map", true,
			nfs_core_param, access_user_map),
	CONF_ITEM_BOOL("Mount_Sys_Tenant_V3", true,
			nfs_core_param, mount_sys_tenant_v3),
	CONF_ITEM_BOOL("Mount_Sys_Tenant_V4", true,
			nfs_core_param, mount_sys_tenant_v4),
	CONF_ITEM_UI64("Time_Threshold", 0, 600, 10,
			nfs_core_param, time_threshold),
	CONF_ITEM_UI64("Nfs_Service_Status", 0, 1024, 0,
			nfs_core_param, nfs_service_status),
	CONF_ITEM_BOOL("Enable_Check_Nfs_Service", true,
			nfs_core_param, enable_check_nfs_service),
	CONF_ITEM_BOOL("Enable_To_Get_Inherit_Acl_V4", true,
			nfs_core_param, enable_to_get_inherit_acl_v4),	
	CONF_ITEM_BOOL("Data_Debug_Log_Write", false,
			nfs_core_param, data_debug_log_write),
	CONF_ITEM_BOOL("Data_Debug_Log_Read", false,
			nfs_core_param, data_debug_log_read),
	CONF_ITEM_BOOL("Enable_Check_Nfs_Service_Thread", true,
			nfs_core_param, enable_check_nfs_service_thread),
	CONF_ITEM_BOOL("Enable_Nfs_Service_Status", false,
			nfs_core_param, enable_nfs_service_status),
       /*add by zhangjiali for probe stats*/
	CONF_ITEM_BOOL("Enable_Probe_Count", true, nfs_core_param, enable_PROBECOUNT),
	CONF_ITEM_BOOL("Enable_Probe_Delay", false, nfs_core_param, enable_PROBEDELAY),
	CONF_ITEM_UI64("Loopback_For_Write_Scale", 0, 100, 100,
		       nfs_core_param, loopback_for_write_scale),
	CONF_ITEM_UI64("Loopback_For_Read_Scale", 0, 100, 100,
		       nfs_core_param, loopback_for_read_scale),
	CONF_ITEM_BOOL("Enable_Getattr_To_Getacl", false,
			nfs_core_param, enable_getattr_to_getacl),
	CONF_ITEM_BOOL("Enable_Poc_Noaccess", false,
			nfs_core_param, enable_poc_noaccess),
	CONF_ITEM_BOOL("Enable_Conf_Global", true,
			nfs_core_param, enable_conf_global),
	CONF_ITEM_BOOL("Enable_Audit_Read", false,
			nfs_core_param, enable_audit_read),
	CONF_ITEM_BOOL("Enable_Audit_Write", false,
			nfs_core_param, enable_audit_write),
	CONF_ITEM_BOOL("Enable_Flag_Rdwr", true,
			nfs_core_param, enable_flag_rdwr),
	CONF_ITEM_UI64("Io_Report_Ism_Time", 0, 600, 15,
			nfs_core_param, io_report_ism_time),
	CONF_ITEM_BOOL("Enable_Report_Node", true,
	      	nfs_core_param, enable_report_node),
	CONF_ITEM_BOOL("Enable_Report_Client", true,
	      	nfs_core_param, enable_report_client),
	CONF_ITEM_BOOL("Enable_Report_Export", true,
	      	nfs_core_param, enable_report_export),
	CONF_ITEM_BOOL("Enable_Report_Tenant", true,
		nfs_core_param, enable_report_tenant),
	CONF_ITEM_BOOL("Enable_Report_Tenant_Client", true,
		nfs_core_param, enable_report_tenant_client),
	CONF_ITEM_BOOL("Enable_Export_Ops_Stats", true,
	      	nfs_core_param, enable_export_ops_stats),
	CONF_ITEM_BOOL("Enable_Tenant_Ops_Stats", true,
	      	nfs_core_param, enable_tenant_ops_stats),
	CONF_ITEM_BOOL("Enable_Tenant_Client_Ops_Stats", true,
	      	nfs_core_param, enable_tenant_client_ops_stats),
	CONF_ITEM_BOOL("Enable_Export_Io_Stat", true,
	      	nfs_core_param, enable_export_io_stat),
	CONF_ITEM_BOOL("Enable_Qos", true,
	      	nfs_core_param, enable_QOS),
	CONF_ITEM_UI64("Qos_Suspend_Count", 0, UINT64_MAX, 0,
	      	nfs_core_param, qos_suspend_count),
	CONF_ITEM_UI64("Qos_Suspend_Count_Max", 0, UINT64_MAX, 10000,
	      	nfs_core_param, qos_suspend_count_max),
	CONF_ITEM_UI64("Readdir_Count_V3", 0, INT64_MAX, 400,
		       nfs_core_param, readdir_count_v3),
	CONF_ITEM_UI64("Readdir_Count_V4", 0, INT64_MAX, 0,
		       nfs_core_param, readdir_count_v4),
	CONF_ITEM_BOOL("Get_Group_List", true,
	      	nfs_core_param, get_group_list),
	CONF_ITEM_BOOL("Get_Local_Group_List", false,
	      	nfs_core_param, get_local_group_list),
	CONF_ITEM_BOOL("Enable_Start_Kill_Session", true,
	      	nfs_core_param, enable_start_kill_session),
	CONF_ITEM_BOOL("Enable_Display_Audit_Nfs_Log", false,
	      	nfs_core_param, enable_display_audit_nfs_log),
	CONFIG_EOL
};

struct config_block nfs_core = {
	.dbus_interface_name = "org.gnfs.nfsd.config.core",
	.blk_desc.name = "NFS_Core_Param",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = core_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};

/**
 * @brief Kerberos/GSSAPI parameters
 */
#ifdef _HAVE_GSSAPI
static struct config_item krb5_params[] = {
	CONF_ITEM_STR("PrincipalName", 1, MAXPATHLEN,
		      DEFAULT_NFS_PRINCIPAL,
		      nfs_krb5_param, svc.principal),
	CONF_ITEM_PATH("KeytabPath", 1, MAXPATHLEN,
		       DEFAULT_NFS_KEYTAB,
		       nfs_krb5_param, keytab),
	CONF_ITEM_PATH("CCacheDir", 1, MAXPATHLEN,
		       DEFAULT_NFS_CCACHE_DIR,
		       nfs_krb5_param, ccache_dir),
	CONF_ITEM_BOOL("Active_krb5", true,
		       nfs_krb5_param, active_krb5),
	CONFIG_EOL
};

struct config_block krb5_param = {
	.dbus_interface_name = "org.gnfs.nfsd.config.krb5",
	.blk_desc.name = "NFS_KRB5",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = krb5_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};
#endif

#ifdef USE_NFSIDMAP
#define GETPWNAMDEF false
#else
#define GETPWNAMDEF true
#endif

/**
 * @brief NFSv4 specific parameters
 */

static struct config_item_list minor_versions[] = {
	CONFIG_LIST_TOK("0", NFSV4_MINOR_VERSION_ZERO),
	CONFIG_LIST_TOK("1", NFSV4_MINOR_VERSION_ONE),
	CONFIG_LIST_TOK("2", NFSV4_MINOR_VERSION_TWO),
	CONFIG_LIST_EOL
};

static struct config_item_list recovery_backend_types[] = {
	CONFIG_LIST_TOK("fs",			RECOVERY_BACKEND_FS),
	CONFIG_LIST_TOK("fs_ng",		RECOVERY_BACKEND_FS_NG),
	CONFIG_LIST_TOK("uds_kv",		RECOVERY_BACKEND_UDS_KV),
	CONFIG_LIST_TOK("uds_ng",		RECOVERY_BACKEND_UDS_NG),
	CONFIG_LIST_TOK("uds_cluster",	RECOVERY_BACKEND_UDS_CLUSTER),
	CONFIG_LIST_EOL
};

static struct config_item version4_params[] = {
	CONF_ITEM_BOOL("Graceless", true,
		       nfs_version4_parameter, graceless),
	CONF_ITEM_UI32("Lease_Lifetime", 0, 120, LEASE_LIFETIME_DEFAULT,
		       nfs_version4_parameter, lease_lifetime),
	CONF_ITEM_UI32("Grace_Period", 0, 180, GRACE_PERIOD_DEFAULT,
		       nfs_version4_parameter, grace_period),
	CONF_ITEM_STR("Server_Scope", 1, MAXNAMLEN, NULL,
		      nfs_version4_parameter, server_scope),
	CONF_ITEM_STR("DomainName", 1, MAXPATHLEN, DOMAINNAME_DEFAULT,
		      nfs_version4_parameter, domainname),
	CONF_ITEM_PATH("IdmapConf", 1, MAXPATHLEN, IDMAPCONF_DEFAULT,
		       nfs_version4_parameter, idmapconf),
	CONF_ITEM_BOOL("UseGetpwnam", GETPWNAMDEF,
		       nfs_version4_parameter, use_getpwnam),
	CONF_ITEM_BOOL("Allow_Numeric_Owners", true,
		       nfs_version4_parameter, allow_numeric_owners),
	CONF_ITEM_BOOL("Only_Numeric_Owners", true,
		       nfs_version4_parameter, only_numeric_owners),
	CONF_ITEM_BOOL("Delegations", false,
		       nfs_version4_parameter, allow_delegations),
	CONF_ITEM_UI32("Deleg_Recall_Retry_Delay", 0, 10,
			DELEG_RECALL_RETRY_DELAY_DEFAULT,
			nfs_version4_parameter, deleg_recall_retry_delay),
	CONF_ITEM_BOOL("PNFS_DMS", false,
		       nfs_version4_parameter, pnfs_dms),
	CONF_ITEM_BOOL("PNFS_DS", false,
		       nfs_version4_parameter, pnfs_ds),
	CONF_ITEM_TOKEN("RecoveryBackend", RECOVERY_BACKEND_DEFAULT,
			recovery_backend_types, nfs_version4_parameter,
			recovery_backend),
	CONF_ITEM_PATH("RecoveryRoot", 1, MAXPATHLEN, NFS_V4_RECOV_ROOT,
		       nfs_version4_parameter, recov_root),
	CONF_ITEM_PATH("RecoveryDir", 1, MAXNAMLEN, NFS_V4_RECOV_DIR,
		       nfs_version4_parameter, recov_dir),
	CONF_ITEM_PATH("RecoveryOldDir", 1, MAXNAMLEN, NFS_V4_OLD_DIR,
		       nfs_version4_parameter, recov_old_dir),
	CONF_ITEM_LIST("minor_versions", NFSV4_MINOR_VERSION_ALL,
		       minor_versions, nfs_version4_parameter, minor_versions),
	CONF_ITEM_UI32("slot_table_size", 1, 1024, NFS41_NB_SLOTS_DEF,
		       nfs_version4_parameter, nb_slots),
	CONF_ITEM_BOOL("Enforce_UTF8_Validation", false,
		       nfs_version4_parameter, enforce_utf8_vld),
	CONF_ITEM_UI32("Max_Client_Ids", 0, UINT32_MAX, 0,
		       nfs_version4_parameter, max_client_ids),
	CONFIG_EOL
};

struct config_block version4_param = {
	.dbus_interface_name = "org.gnfs.nfsd.config.nfsv4",
	.blk_desc.name = "NFSv4",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = version4_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};
static struct config_item rdma_params[] = {
	CONF_ITEM_BOOL("Enable_RDMA", true,
			_rdma_param, enable_rdma),
	CONF_ITEM_UI32("NFS_RDMA_Port", 0, UINT32_MAX, 20049,
			_rdma_param, nfsrdma_port),
	CONF_ITEM_BOOL("Enable_Thread_Nolock", true,
			_rdma_param, enable_thread_nolock),
	CONF_ITEM_BOOL("Enable_Rpc_Thread", true,
			_rdma_param, enable_rpc_thread),
	/*  thread config */
	CONF_ITEM_UI32("RDMA_Cq_ThrdMax", 0, 255, 1,
			_rdma_param, thread_cq_num),
	CONF_ITEM_UI32("RDMA_Rpc_ThrdMax", 0, 255, 1,
			_rdma_param, thread_rpc_num),
	CONF_ITEM_UI32("RDMA_Work_ThrdMax", 0, 255, 64,
			_rdma_param, thread_work_num),
	/* thread config end*/
	CONF_ITEM_UI32("Fabric_Tx_Thread", 0, 32, 2,
			_rdma_param, thread_tx_num),
	CONF_ITEM_UI32("Fabric_Rx_Thread", 0, 32, 3,
			_rdma_param, thread_rx_num),
	CONF_ITEM_BOOL("Enable_Workth_Send", false,
			_rdma_param, enable_workth_send),
	/**memery config start */
	/*send memery pool size = Depth*Chunk_Size*/
	CONF_ITEM_UI32("Rdma_Sendq_Depth", 0, 100000, 4096,
		       _rdma_param, rdma_sendq_depth),
	CONF_ITEM_UI32("Rdma_Sendq_Chunk_Size", 0, 10*1048576, 1048576,
		       _rdma_param, rdma_sendq_chunk_size),
	/*recv memery pool size = Depth*Chunk_Size*/
	CONF_ITEM_UI32("Rdma_Mempool_Size", 0, 100000, 32768,
			_rdma_param, rdma_mempool_size),
	CONF_ITEM_UI32("Rdma_Chunk_Size", 0, 1048576, 65536,
			_rdma_param, rdma_chunk_size),
	/*recv memery list count*/
	CONF_ITEM_UI32("Rdma_Recvq_Depth", 0, 100000, 32768,
			_rdma_param, rdma_recvq_depth),
	/**memery config end */
	CONF_ITEM_UI32("Rdma_Qp_Sq_Length", 0, 10000, 4000,
		       _rdma_param, rdma_qp_sq_length),
	CONF_ITEM_UI32("Rdma_Qp_Rq_Length", 0, 10000, 128,
			_rdma_param, rdma_qp_rq_length),
	CONF_ITEM_UI32("Rdma_Mem_Align", 0, 32768, 4096,
			_rdma_param, rdma_mem_align),
	CONF_ITEM_UI32("Rdma_Mem_Affinity_0", 0, 32, 1,
			_rdma_param, rdma_mem_affinity_0),
	CONF_ITEM_UI32("Rdma_Mem_Affinity_1", 0, 32, 6,
			_rdma_param, rdma_mem_affinity_1),
	CONF_ITEM_BOOL("Rdma_Use_Hugepage", false,
			_rdma_param, hugepage_enable),
	CONF_ITEM_UI32("Cq_Cpu_Affinity_0", 1, 256, 8,
		    _rdma_param, cq_cpu_affinity_0),
	CONF_ITEM_UI32("Cq_Cpu_Affinity_1", 1, 256, 38,
		    _rdma_param, cq_cpu_affinity_1),
	CONF_ITEM_STR("Worker_Cpu_Affinity_0", 1, 255, "6,7,54,55,56",
		    _rdma_param, worker_cpu_affinity_0),
	CONF_ITEM_STR("Worker_Cpu_Affinity_1", 1, 255, "36,37,84,85,86",
		    _rdma_param, worker_cpu_affinity_1),
	CONF_ITEM_UI32("Fabric_Wait_Destory_Ep_Num_Max", 1, 256, 20,
		    _rdma_param, rdma_wait_destory_ep_num_max),
	CONF_ITEM_UI32("Fabric_Wait_Destory_Ep_Num_Max_Abort", 1, 256, 25,
		    _rdma_param, rdma_wait_destory_ep_num_max_abort),
	CONF_ITEM_UI32("Rdma_Workthrd_Using_count", 0, 1024, 12,
		       _rdma_param, rdma_workthrd_using_count),
	CONF_ITEM_STR("Thread_Cpu_Affinity_0", 1, 255, "",
		    _rdma_param, thread_cpu_affinity_0),
	CONF_ITEM_STR("Thread_Cpu_Affinity_1", 1, 255, "",
		    _rdma_param, thread_cpu_affinity_1),
	CONF_ITEM_UI32("Work_Sleep_Time", 0, INT32_MAX, 300,
		       _rdma_param, work_sleep_time),
	CONF_ITEM_BOOL("Enable_Fabric_Inline", false,
			_rdma_param, enable_fabric_inline),
	CONF_ITEM_BOOL("Enable_Debug_Cq", false,
			_rdma_param, enable_debug_cq),
	CONF_ITEM_BOOL("Enable_Cpu_Affinity", false,
			_rdma_param, enable_cpu_affinity),
	CONF_ITEM_BOOL("Enable_Credicts_Countable", false,
			_rdma_param, enable_credicts_countable),
	CONF_ITEM_UI64("Cred_Queues_Num", 0, 100, 2,
			_rdma_param, cred_queues_num),
	CONF_ITEM_UI64("Qp_Cq_Length", 0, 100000, 60000,
			_rdma_param, qp_cq_length),
	CONFIG_EOL
};

struct config_block rdma_config = {
	.dbus_interface_name = "org.ganesha.nfsd.config.rdma",
	.blk_desc.name = "RDMA",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = rdma_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};

/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright 2015-2019 Red Hat, Inc. and/or its affiliates.
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 */

/**
 * @addtogroup FSAL_MDCACHE
 * @{
 */

/**
 * @file  mdcache_helpers.c
 * @brief Miscellaneous helper functions
 */

#include "config.h"
#include "fsal.h"
#include "nfs4_acls.h"
#include "mdcache_hash.h"
#include "mdcache_int.h"
#include "nfs_core.h"
#include "nfs4_fs_locations.h"
#include "fridgethr.h"

/* Async dentry deletion support */
typedef struct dentry_del_q {
	struct dentry_del_q *next;
	struct dentry_del_q *prev;
	struct gsh_buffdesc file_obj;
	struct gsh_buffdesc dir_obj;
	char *name;
	uint32_t flags;
	const struct fsal_up_vector *vec;
	/* Copy of file object data */
	char file_data[];
} dentry_del_q_t, *dentry_del_q_ptr;

struct dentry_del_q_lane {
	struct dentry_del_q del_q;
	pthread_mutex_t mtx;
	uint32_t size;
	uint64_t totalnum;
	uint64_t totaldelnum;
};

static struct dentry_del_q_lane dentry_del_q_lane;
static struct fridgethr *dentry_cleanup_fridge;

/* Async dentry deletion infrastructure */

/* Queue manipulation functions */
static inline void dentry_del_q_init(struct dentry_del_q *head)
{
	head->next = head;
	head->prev = head;
}

static inline void dentry_del_q_add_tail(struct dentry_del_q *head,
					  struct dentry_del_q *new)
{
	new->prev = head->prev;
	new->next = head;
	head->prev->next = new;
	head->prev = new;
}

static inline void dentry_del_q_del(struct dentry_del_q *node)
{
	struct dentry_del_q *left = node->prev;
	struct dentry_del_q *right = node->next;
	if (left != NULL)
		left->next = right;
	if (right != NULL)
		right->prev = left;
	node->next = NULL;
	node->prev = NULL;
}

/**
 * @brief Put dentry deletion request into async queue
 *
 * @param[in] vec      Up ops vector
 * @param[in] dir_obj  The directory object
 * @param[in] file_obj The file object being invalidated
 * @param[in] name     Name of the entry being invalidated
 * @param[in] flags    Flags for invalidation behavior
 *
 * @return 0 on success, error code on failure
 */
static int put_dentry_del_queue(const struct fsal_up_vector *vec,
				 struct gsh_buffdesc *dir_obj,
				 struct gsh_buffdesc *file_obj,
				 const char *name,
				 uint32_t flags)
{
	dentry_del_q_ptr del_head = &dentry_del_q_lane.del_q;
	dentry_del_q_ptr del_new;
	size_t name_len = name ? strlen(name) : 0;
	size_t file_data_len = file_obj ? file_obj->len : 0;
	size_t dir_data_len = dir_obj ? dir_obj->len : 0;
	size_t total_size = sizeof(dentry_del_q_t) + file_data_len + dir_data_len + name_len + 1;

	del_new = gsh_malloc(total_size);
	if (!del_new) {
		LogFatal(COMPONENT_CACHE_INODE_LRU,
			 "[async delete] put dentry fail: name %s", name ? name : "unknown");
		return ENOMEM;
	}

	/* Initialize the new queue entry */
	del_new->vec = vec;
	del_new->flags = flags;

	/* Copy file object data */
	if (file_obj && file_obj->len > 0) {
		del_new->file_obj.len = file_obj->len;
		del_new->file_obj.addr = del_new->file_data;
		memcpy(del_new->file_data, file_obj->addr, file_obj->len);
	} else {
		del_new->file_obj.len = 0;
		del_new->file_obj.addr = NULL;
	}

	/* Copy directory object data */
	if (dir_obj && dir_obj->len > 0) {
		del_new->dir_obj.len = dir_obj->len;
		del_new->dir_obj.addr = del_new->file_data + file_data_len;
		memcpy(del_new->file_data + file_data_len, dir_obj->addr, dir_obj->len);
	} else {
		del_new->dir_obj.len = 0;
		del_new->dir_obj.addr = NULL;
	}

	/* Copy name */
	if (name) {
		del_new->name = del_new->file_data + file_data_len + dir_data_len;
		strcpy(del_new->name, name);
	} else {
		del_new->name = NULL;
	}

	/* Add to queue */
	PTHREAD_MUTEX_lock(&dentry_del_q_lane.mtx);
	dentry_del_q_add_tail(del_head, del_new);
	++dentry_del_q_lane.size;
	++dentry_del_q_lane.totalnum;
	PTHREAD_MUTEX_unlock(&dentry_del_q_lane.mtx);

	LogDebug(COMPONENT_CACHE_INODE_LRU,
		 "[async delete] total: %lu, del total: %lu, "
		 "put dentry, current num: %u, name %s",
		 (unsigned long)(dentry_del_q_lane.totalnum),
		 (unsigned long)(dentry_del_q_lane.totaldelnum),
		 dentry_del_q_lane.size, name ? name : "unknown");

	return 0;
}

/**
 * @brief Delete a single dentry queue entry
 *
 * @param[in] node The queue entry to delete
 */
static void dentry_del_q_remove(dentry_del_q_ptr node)
{
	PTHREAD_MUTEX_lock(&dentry_del_q_lane.mtx);
	dentry_del_q_del(node);
	gsh_free(node);
	--dentry_del_q_lane.size;
	++dentry_del_q_lane.totaldelnum;
	PTHREAD_MUTEX_unlock(&dentry_del_q_lane.mtx);
}

/**
 * @brief Delete all dentry queue entries
 */
static void dentry_del_q_remove_all(void)
{
	dentry_del_q_ptr node = NULL, noden = NULL;

	glist_for_each_safe(node, noden, &dentry_del_q_lane.del_q) {
		LogDebug(COMPONENT_CACHE_INODE,
			 "[async delete] del all dentry: %s",
			 node->name ? node->name : "unknown");

		PTHREAD_MUTEX_lock(&dentry_del_q_lane.mtx);
		dentry_del_q_del(node);
		gsh_free(node);
		--dentry_del_q_lane.size;
		++dentry_del_q_lane.totaldelnum;
		PTHREAD_MUTEX_unlock(&dentry_del_q_lane.mtx);
	}
}

/**
 * @brief Process a single dentry deletion request
 *
 * @param[in] node The deletion request to process
 *
 * @return true if entry was processed and should be removed from queue
 */
static bool process_dentry_deletion(dentry_del_q_ptr node)
{
	mdcache_entry_t *file_entry = NULL;
	mdcache_entry_t *dir_entry = NULL;
	mdcache_key_t key;
	fsal_status_t status;
	struct req_op_context op_context;

	/* Initialize operation context */
	init_op_context_simple(&op_context, node->vec->up_gsh_export,
			       node->vec->up_fsal_export);

	/* Find file entry if file_obj is provided */
	if (node->file_obj.addr && node->file_obj.len > 0) {
		struct mdcache_fsal_export *exp = mdc_export(node->vec->up_fsal_export);
		if (exp) {
			status = mdcache_locate_host(&node->file_obj, exp, &file_entry, NULL);
			if (FSAL_IS_ERROR(status)) {
				LogDebug(COMPONENT_CACHE_INODE,
					 "[async delete] file entry not found for %s",
					 node->name ? node->name : "unknown");
			} else if (file_entry != NULL) {
				LogDebug(COMPONENT_CACHE_INODE,
					 "[async delete] processing file entry %p for %s",
					 file_entry, node->name ? node->name : "unknown");

				/* Mark entry as unreachable for proper cleanup */
				mdc_unreachable(file_entry);
				mdcache_put(file_entry);
			}
		}
	}

	/* Find directory entry if dir_obj is provided */
	if (node->dir_obj.addr && node->dir_obj.len > 0) {
		struct mdcache_fsal_export *exp = mdc_export(node->vec->up_fsal_export);
		if (exp) {
			status = mdcache_locate_host(&node->dir_obj, exp, &dir_entry, NULL);
			if (!FSAL_IS_ERROR(status) && dir_entry != NULL) {
				LogDebug(COMPONENT_CACHE_INODE,
					 "[async delete] processing dir entry %p for %s",
					 dir_entry, node->name ? node->name : "unknown");

				/* Remove directory entry if name is provided */
				if (node->name) {
					PTHREAD_RWLOCK_wrlock(&dir_entry->content_lock);
					mdcache_dirent_remove(dir_entry, node->name);
					PTHREAD_RWLOCK_unlock(&dir_entry->content_lock);
					LogDebug(COMPONENT_CACHE_INODE,
						 "[async delete] removed dirent %s from directory cache",
						 node->name);
				}
				mdcache_put(dir_entry);
			}
		}
	}

	/* Clean up operation context */
	release_op_context();

	return true; /* Always remove from queue after processing */
}

/**
 * @brief Async cleanup thread for dentry deletions
 *
 * @param[in] ctx Fridgethr context
 */
static void mdcache_async_dentry_cleanup(struct fridgethr_context *ctx)
{
	uint32_t totalnum = 0;
	uint32_t deletednum = 0;
	dentry_del_q_ptr node = NULL;
	dentry_del_q_ptr noden = NULL;

	totalnum = dentry_del_q_lane.size;

	if (totalnum == 0) {
		/* Nothing to process */
		return;
	}

	LogDebug(COMPONENT_CACHE_INODE_LRU,
		 "[async delete] starting cleanup, queue size: %u", totalnum);

	glist_for_each_safe(node, noden, &dentry_del_q_lane.del_q) {
		if (process_dentry_deletion(node)) {
			dentry_del_q_remove(node);
			deletednum++;
		}
	}

	LogDebug(COMPONENT_CACHE_INODE_LRU,
		 "[async delete] total: %lu, del total: %lu, "
		 "this time handle: current %u, before: %u, deleted: %u",
		 (unsigned long)(dentry_del_q_lane.totalnum),
		 (unsigned long)(dentry_del_q_lane.totaldelnum),
		 dentry_del_q_lane.size, totalnum, deletednum);
}

static fsal_status_t
mdc_up_invalidate(const struct fsal_up_vector *vec, struct gsh_buffdesc *handle,
		  uint32_t flags)
{
	mdcache_entry_t *entry;
	fsal_status_t status;
	struct req_op_context op_context;
	mdcache_key_t key;

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	key.fsal = vec->up_fsal_export->sub_export->fsal;
	cih_hash_key(&key, vec->up_fsal_export->sub_export->fsal, handle,
		     CIH_HASH_KEY_PROTOTYPE);

	status = mdcache_find_keyed(&key, &entry);
	if (status.major == ERR_FSAL_NOENT) {
		/* Not cached, so invalidate is a success */
		status = fsalstat(ERR_FSAL_NO_ERROR, 0);
		goto out;
	} else if (FSAL_IS_ERROR(status)) {
		/* Real error */
		goto out;
	}

	atomic_clear_uint32_t_bits(&entry->mde_flags,
				   flags & FSAL_UP_INVALIDATE_CACHE);

	if (flags & FSAL_UP_INVALIDATE_CLOSE)
		status = fsal_close(&entry->obj_handle);

	if (flags & FSAL_UP_INVALIDATE_PARENT &&
	    entry->obj_handle.type == DIRECTORY) {
		PTHREAD_RWLOCK_wrlock(&entry->content_lock);
		/* Clean up parent key */
		mdcache_free_fh(&entry->fsobj.fsdir.parent);
		PTHREAD_RWLOCK_unlock(&entry->content_lock);
	}

	mdcache_put(entry);

out:

	release_op_context();
	return status;
}

/** Release a cache entry if it's otherwise idle.
 *
 * @param[in] vec    Up ops vector
 * @param[in] handle Handle-key that should be vetted and possibly removed
 * @param[in] flags  Unused, for future expansion
 *
 * @return FSAL status. (ERR_FSAL_NO_ERROR indicates that one was released)
 */
static fsal_status_t
mdc_up_try_release(const struct fsal_up_vector *vec,
		   struct gsh_buffdesc *handle, uint32_t flags)
{
	mdcache_entry_t *entry;
	mdcache_key_t key;
	cih_latch_t latch;
	int32_t refcnt;
	fsal_status_t ret;

	/* flags are for future expansion. For now, we don't accept any. */
	if (flags)
		return fsalstat(ERR_FSAL_INVAL, 0);

	/*
	 * Find the entry, and keep the wrlock on the partition. This ensures
	 * that no other caller can find this entry in the hashtable and
	 * race in to take a reference.
	 */
	key.fsal = vec->up_fsal_export->sub_export->fsal;
	cih_hash_key(&key, vec->up_fsal_export->sub_export->fsal, handle,
		     CIH_HASH_KEY_PROTOTYPE);

	entry = cih_get_by_key_latch(&key, &latch,
				     CIH_GET_WLOCK | CIH_GET_UNLOCK_ON_MISS,
				     __func__, __LINE__);
	if (!entry) {
		LogDebug(COMPONENT_CACHE_INODE, "no entry found");
		return fsalstat(ERR_FSAL_STALE, 0);
	}

	/*
	 * We can remove it if the only ref is the sentinel. We can't put
	 * the last ref while holding the latch though, so we must take an
	 * extra reference, remove it and then put the extra ref after
	 * releasing the latch.
	 */
	refcnt = atomic_fetch_int32_t(&entry->lru.refcnt);
	LogDebug(COMPONENT_CACHE_INODE, "entry %p has refcnt of %d", entry,
		 refcnt);
	if (refcnt == 1) {
		mdcache_get(entry);
		cih_remove_latched(entry, &latch, 0);
		ret = fsalstat(ERR_FSAL_NO_ERROR, 0);
	} else {
		ret = fsalstat(ERR_FSAL_STILL_IN_USE, 0);
	}
	cih_hash_release(&latch);
	if (refcnt == 1)
		mdcache_put(entry);
	return ret;
}

/**
 * @brief Update cached attributes
 *
 * @param[in] vec    Up ops vector
 * @param[in] handle Export containing object
 * @param[in] attr   New attributes
 * @param[in] flags  Flags to govern update
 *
 * @return FSAL status
 */

static fsal_status_t
mdc_up_update(const struct fsal_up_vector *vec, struct gsh_buffdesc *handle,
	      struct fsal_attrlist *attr, uint32_t flags)
{
	mdcache_entry_t *entry;
	fsal_status_t status;
	/* Have necessary changes been made? */
	bool mutatis_mutandis = false;
	struct req_op_context op_context;
	mdcache_key_t key;
	attrmask_t mask_set = 0;

	/* These cannot be updated, changing any of them is
	   tantamount to destroying and recreating the file. */
	if (FSAL_TEST_MASK
	    (attr->valid_mask,
	     ATTR_TYPE | ATTR_FSID | ATTR_FILEID | ATTR_RAWDEV | ATTR_RDATTR_ERR
	     | ATTR_GENERATION)) {
		return fsalstat(ERR_FSAL_INVAL, 0);
	}

	/* Filter out garbage flags */

	if (flags &
	    ~(fsal_up_update_filesize_inc | fsal_up_update_atime_inc |
	      fsal_up_update_creation_inc | fsal_up_update_ctime_inc |
	      fsal_up_update_mtime_inc |
	      fsal_up_update_spaceused_inc | fsal_up_nlink)) {
		return fsalstat(ERR_FSAL_INVAL, 0);
	}

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	key.fsal = vec->up_fsal_export->sub_export->fsal;
	cih_hash_key(&key, vec->up_fsal_export->sub_export->fsal, handle,
		     CIH_HASH_KEY_PROTOTYPE);

	status = mdcache_find_keyed(&key, &entry);
	if (status.major == ERR_FSAL_NOENT) {
		/* Not cached, so invalidate is a success */
		status = fsalstat(ERR_FSAL_NO_ERROR, 0);
		goto out;
	} else if (FSAL_IS_ERROR(status)) {
		/* Real error */
		goto out;
	}

	/* Knock things out if the link count falls to 0. */

	if ((flags & fsal_up_nlink) && (attr->numlinks == 0)) {
		LogFullDebug(COMPONENT_CACHE_INODE,
			     "Entry %p Clearing MDCACHE_TRUST_ATTRS, MDCACHE_TRUST_CONTENT, MDCACHE_DIR_POPULATED",
			     entry);
		atomic_clear_uint32_t_bits(&entry->mde_flags,
					   MDCACHE_TRUST_ATTRS |
					   MDCACHE_TRUST_CONTENT |
					   MDCACHE_DIR_POPULATED);

		status = fsal_close(&entry->obj_handle);

		if (FSAL_IS_ERROR(status))
			goto put;
	}

	if (attr->valid_mask == 0) {
		/* Done */
		goto put;
	}

	/* If the attributes are invalid, we can't update a subset.  Just bail,
	 * and update them on demand */
	if (!mdcache_test_attrs_trust(entry, attr->valid_mask)) {
		goto put;
	}

	PTHREAD_RWLOCK_wrlock(&entry->attr_lock);

	if (attr->expire_time_attr != 0)
		entry->attrs.expire_time_attr = attr->expire_time_attr;

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_SIZE)) {
		if (flags & fsal_up_update_filesize_inc) {
			if (attr->filesize > entry->attrs.filesize) {
				entry->attrs.filesize = attr->filesize;
				mutatis_mutandis = true;
				mask_set |= ATTR_SIZE;
			}
		} else {
			entry->attrs.filesize = attr->filesize;
			mutatis_mutandis = true;
			mask_set |= ATTR_SIZE;
		}
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_SPACEUSED)) {
		if (flags & fsal_up_update_spaceused_inc) {
			if (attr->spaceused > entry->attrs.spaceused) {
				entry->attrs.spaceused = attr->spaceused;
				mutatis_mutandis = true;
				mask_set |= ATTR_SPACEUSED;
			}
		} else {
			entry->attrs.spaceused = attr->spaceused;
			mutatis_mutandis = true;
			mask_set |= ATTR_SPACEUSED;
		}
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_ACL)) {
		/**
		 * @todo Someone who knows the ACL code, please look
		 * over this.  We assume that the FSAL takes a
		 * reference on the supplied ACL that we can then hold
		 * onto.  This seems the most reasonable approach in
		 * an asynchronous call.
		 */

		nfs4_acl_release_entry(entry->attrs.acl);

		entry->attrs.acl = attr->acl;
		mutatis_mutandis = true;
		mask_set |= ATTR_ACL;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_MODE)) {
		entry->attrs.mode = attr->mode;
		mutatis_mutandis = true;
		mask_set |= ATTR_MODE;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_NUMLINKS)) {
		entry->attrs.numlinks = attr->numlinks;
		mutatis_mutandis = true;
		mask_set |= ATTR_NUMLINKS;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_OWNER)) {
		entry->attrs.owner = attr->owner;
		mutatis_mutandis = true;
		mask_set |= ATTR_OWNER;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_GROUP)) {
		entry->attrs.group = attr->group;
		mutatis_mutandis = true;
		mask_set |= ATTR_GROUP;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_ATIME)
	    && ((flags & ~fsal_up_update_atime_inc)
		||
		(gsh_time_cmp(&attr->atime, &entry->attrs.atime) == 1))) {
		entry->attrs.atime = attr->atime;
		mutatis_mutandis = true;
		mask_set |= ATTR_ATIME;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_CREATION)
	    && ((flags & ~fsal_up_update_creation_inc)
		||
		(gsh_time_cmp(&attr->creation, &entry->attrs.creation) == 1))) {
		entry->attrs.creation = attr->creation;
		mutatis_mutandis = true;
		mask_set |= ATTR_CREATION;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_CTIME)
	    && ((flags & ~fsal_up_update_ctime_inc)
		||
		(gsh_time_cmp(&attr->ctime, &entry->attrs.ctime) == 1))) {
		entry->attrs.ctime = attr->ctime;
		mutatis_mutandis = true;
		mask_set |= ATTR_CTIME;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_MTIME)
	    && ((flags & ~fsal_up_update_mtime_inc)
		||
		(gsh_time_cmp(&attr->mtime, &entry->attrs.mtime) == 1))) {
		entry->attrs.mtime = attr->mtime;
		mutatis_mutandis = true;
		mask_set |= ATTR_MTIME;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR_CHANGE)) {
		entry->attrs.change = attr->change;
		mutatis_mutandis = true;
		mask_set |= ATTR_CHANGE;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR4_FS_LOCATIONS)) {
		nfs4_fs_locations_release(entry->attrs.fs_locations);

		entry->attrs.fs_locations = attr->fs_locations;
		mutatis_mutandis = true;
		mask_set |= ATTR4_FS_LOCATIONS;
	}

	if (FSAL_TEST_MASK(attr->valid_mask, ATTR4_SEC_LABEL)) {
		gsh_free(entry->attrs.sec_label.slai_data.slai_data_val);
		entry->attrs.sec_label = attr->sec_label;
		attr->sec_label.slai_data.slai_data_len = 0;
		attr->sec_label.slai_data.slai_data_val = NULL;
		mutatis_mutandis = true;
		mask_set |= ATTR4_SEC_LABEL;
	}

	if (mutatis_mutandis) {
		mdc_fixup_md(entry, attr);
		entry->attrs.valid_mask |= mask_set;
		/* If directory can not trust content anymore. */
		if (entry->obj_handle.type == DIRECTORY) {
			LogFullDebug(COMPONENT_CACHE_INODE,
				     "Entry %p Clearing MDCACHE_TRUST_CONTENT, MDCACHE_DIR_POPULATED",
				     entry);
			atomic_clear_uint32_t_bits(&entry->mde_flags,
						   MDCACHE_TRUST_CONTENT |
						   MDCACHE_DIR_POPULATED);
		}
		status = fsalstat(ERR_FSAL_NO_ERROR, 0);
	} else {
		atomic_clear_uint32_t_bits(&entry->mde_flags,
					   MDCACHE_TRUST_ATTRS);
		status = fsalstat(ERR_FSAL_INVAL, 0);
	}

	PTHREAD_RWLOCK_unlock(&entry->attr_lock);

put:
	mdcache_put(entry);
out:

	release_op_context();
	return status;
}

/**
 * @brief Invalidate a cached entry
 *
 * @note doesn't need op_ctx, handled in mdc_up_invalidate
 *
 * @param[in] vec    Up ops vector
 * @param[in] key    Key to specify object
 * @param[in] flags  FSAL_UP_INVALIDATE*
 *
 * @return FSAL status
 */

static fsal_status_t
mdc_up_invalidate_close(const struct fsal_up_vector *vec,
			struct gsh_buffdesc *key, uint32_t flags)
{
	fsal_status_t status;

	status = up_async_invalidate(general_fridge, vec, key,
				     flags | FSAL_UP_INVALIDATE_CLOSE,
				     NULL, NULL);
	return status;
}

/** Grant a lock to a client
 *
 * Pass up to upper layer
 *
 * @param[in] vec	Up ops vector
 * @param[in] file      The file in question
 * @param[in] owner     The lock owner
 * @param[in] lock_param   A description of the lock
 *
 */
state_status_t mdc_up_lock_grant(const struct fsal_up_vector *vec,
				 struct gsh_buffdesc *file,
				 void *owner,
				 fsal_lock_param_t *lock_param)
{
	struct mdcache_fsal_export *myself = mdc_export(vec->up_fsal_export);
	state_status_t rc;
	struct req_op_context op_context;

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	rc = myself->super_up_ops.lock_grant(vec, file, owner, lock_param);

	release_op_context();

	return rc;
}

/** Signal lock availability
 *
 * Pass up to upper layer
 *
 * @param[in] vec	   Up ops vector
 * @param[in] file	 The file in question
 * @param[in] owner        The lock owner
 * @param[in] lock_param   A description of the lock
 *
 */
state_status_t mdc_up_lock_avail(const struct fsal_up_vector *vec,
				 struct gsh_buffdesc *file,
				 void *owner,
				 fsal_lock_param_t *lock_param)
{
	struct mdcache_fsal_export *myself = mdc_export(vec->up_fsal_export);
	state_status_t rc;
	struct req_op_context op_context;

	/* Initialize op context */
	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	rc = myself->super_up_ops.lock_avail(vec, file, owner, lock_param);

	release_op_context();

	return rc;
}

/** Perform a layoutrecall on a single file
 *
 * Pass to upper layer
 *
 * @param[in] vec	   Up ops vector
 * @param[in] handle       Handle on which the layout is held
 * @param[in] layout_type  The type of layout to recall
 * @param[in] changed      Whether the layout has changed and the
 *                         client ought to finish writes through DMS
 * @param[in] segment      Segment to recall
 * @param[in] cookie       A cookie returned with the return that
 *                         completely satisfies a recall
 * @param[in] spec         Lets us be fussy about what clients we send
 *                         to. May beNULL.
 *
 */
state_status_t mdc_up_layoutrecall(const struct fsal_up_vector *vec,
				   struct gsh_buffdesc *handle,
				   layouttype4 layout_type,
				   bool changed,
				   const struct pnfs_segment *segment,
				   void *cookie,
				   struct layoutrecall_spec *spec)
{
	struct mdcache_fsal_export *myself = mdc_export(vec->up_fsal_export);
	state_status_t rc;
	struct req_op_context op_context;

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	rc = myself->super_up_ops.layoutrecall(vec, handle, layout_type,
					       changed, segment, cookie, spec);

	release_op_context();

	return rc;
}

/** Recall a delegation
 *
 * Pass to upper layer
 *
 * @param[in] vec	Up ops vector
 * @param[in] handle Handle on which the delegation is held
 */
state_status_t mdc_up_delegrecall(const struct fsal_up_vector *vec,
				  struct gsh_buffdesc *handle)
{
	struct mdcache_fsal_export *myself = mdc_export(vec->up_fsal_export);
	state_status_t rc;
	struct req_op_context op_context;

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	rc = myself->super_up_ops.delegrecall(vec, handle);

	release_op_context();

	return rc;
}

/**
 * @brief Handle directory entry invalidation with proper cleanup
 *
 * This function handles directory entry invalidation when files are
 * deleted or renamed. It performs proper cache cleanup including
 * marking entries as unreachable to ensure storage space is released.
 *
 * @param[in] vec      Up ops vector
 * @param[in] dir_obj  The directory object
 * @param[in] file_obj The file object being invalidated
 * @param[in] name     Name of the entry being invalidated
 * @param[in] flags    Flags for invalidation behavior
 *
 * @return FSAL status
 */
static fsal_status_t mdc_up_dentry(const struct fsal_up_vector *vec,
				   struct gsh_buffdesc *dir_obj,
				   struct gsh_buffdesc *file_obj,
				   const char *name,
				   uint32_t flags)
{
	LogDebug(COMPONENT_CACHE_INODE,
		 "MDCACHE dentry invalidation for file entry, name='%s', async=%s",
		 name, mdcache_param.async_dentry_enabled ? "true" : "false");

	/* Check if async mode is enabled */
	if (mdcache_param.async_dentry_enabled) {
		int ret = put_dentry_del_queue(vec, dir_obj, file_obj, name, flags);
		if (ret == 0) {
			LogDebug(COMPONENT_CACHE_INODE,
				 "MDCACHE dentry queued for async deletion, name='%s'", name);
			return fsalstat(ERR_FSAL_NO_ERROR, 0);
		} else {
			LogWarn(COMPONENT_CACHE_INODE,
				"Failed to queue dentry for async deletion, falling back to sync mode");
			/* Fall through to synchronous processing */
		}
	}

	/* Synchronous processing (original implementation) */
	mdcache_entry_t *file_entry = NULL;
	fsal_status_t status;
	struct req_op_context op_context;
	mdcache_key_t key;

	/* Get a ref to the vec->up_gsh_export and initialize op_context for the
	 * upcall
	 */
	get_gsh_export_ref(vec->up_gsh_export);
	init_op_context_simple(&op_context, vec->up_gsh_export,
			       vec->up_fsal_export);

	/* Construct cache key for file lookup */
	key.fsal = vec->up_fsal_export->sub_export->fsal;
	cih_hash_key(&key, vec->up_fsal_export->sub_export->fsal, file_obj,
		     CIH_HASH_KEY_PROTOTYPE);

	/* Find the file entry in cache */
	status = mdcache_find_keyed(&key, &file_entry);
	if (!FSAL_IS_ERROR(status) && file_entry != NULL) {
		LogDebug(COMPONENT_CACHE_INODE,
			 "Found file entry %p for dentry invalidation, marking as unreachable",
			 file_entry);

		/* Mark the entry as unreachable - this will set it up for proper cleanup
		 * including LRU state management that triggers state_wipe_file() */
		mdc_unreachable(file_entry);

		/* Put the reference we got from mdcache_find_keyed */
		mdcache_put(file_entry);

		status = fsalstat(ERR_FSAL_NO_ERROR, 0);
	} else {
		LogDebug(COMPONENT_CACHE_INODE,
			 "File entry not found in cache (status=%s), using standard invalidate",
			 fsal_err_txt(status));

		/* Fallback to standard invalidation if entry not found */
		status = mdc_up_invalidate(vec, file_obj, FSAL_UP_INVALIDATE_CACHE);
	}

	/* Also invalidate directory entry if dir_obj is provided */
	if (dir_obj != NULL && dir_obj->addr != NULL) {
		fsal_status_t dir_status;

		LogDebug(COMPONENT_CACHE_INODE,
			 "Also invalidating directory entry for name='%s'", name);

		dir_status = mdc_up_invalidate(vec, dir_obj, FSAL_UP_INVALIDATE_CACHE);
		if (FSAL_IS_ERROR(dir_status)) {
			LogDebug(COMPONENT_CACHE_INODE,
				 "Directory invalidation failed: %s", fsal_err_txt(dir_status));
		}
	}

	release_op_context();

	LogDebug(COMPONENT_CACHE_INODE,
		 "MDCACHE dentry invalidation completed with status: %s",
		 fsal_err_txt(status));

	return status;
}

fsal_status_t
mdcache_export_up_ops_init(struct fsal_up_vector *my_up_ops,
			   const struct fsal_up_vector *super_up_ops)
{
	/* Init with super ops. Struct copy */
	*my_up_ops = *super_up_ops;

	up_ready_init(my_up_ops);

	/* Replace cache-related calls */
	my_up_ops->invalidate = mdc_up_invalidate;
	my_up_ops->update = mdc_up_update;
	my_up_ops->invalidate_close = mdc_up_invalidate_close;
	my_up_ops->try_release = mdc_up_try_release;
	my_up_ops->dentry = mdc_up_dentry;

	/* These are pass-through calls that set op_ctx */
	my_up_ops->lock_grant = mdc_up_lock_grant;
	my_up_ops->lock_avail = mdc_up_lock_avail;
	my_up_ops->layoutrecall = mdc_up_layoutrecall;
	/* notify_device cannot call into MDCACHE */
	my_up_ops->delegrecall = mdc_up_delegrecall;

	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

/**
 * @brief Initialize async dentry deletion subsystem
 *
 * @return FSAL status
 */
fsal_status_t mdcache_async_dentry_init(void)
{
	int code = 0;
	struct fridgethr_params frp;

	if (!mdcache_param.async_dentry_enabled) {
		LogInfo(COMPONENT_CACHE_INODE_LRU,
			"Async dentry deletion is disabled");
		return fsalstat(ERR_FSAL_NO_ERROR, 0);
	}

	if (dentry_cleanup_fridge) {
		/* Already initialized */
		LogInfo(COMPONENT_CACHE_INODE_LRU,
			"Async dentry deletion already initialized");
		return fsalstat(ERR_FSAL_NO_ERROR, 0);
	}

	/* Initialize the queue */
	dentry_del_q_init(&dentry_del_q_lane.del_q);
	PTHREAD_MUTEX_init(&dentry_del_q_lane.mtx, NULL);
	dentry_del_q_lane.size = 0;
	dentry_del_q_lane.totalnum = 0;
	dentry_del_q_lane.totaldelnum = 0;

	/* Set up fridgethr parameters */
	memset(&frp, 0, sizeof(struct fridgethr_params));
	frp.thr_max = 1;
	frp.thr_min = 1;
	frp.thread_delay = 1000; /* 1 second interval */
	frp.flavor = fridgethr_flavor_looper;

	/* Initialize the cleanup thread */
	code = fridgethr_init(&dentry_cleanup_fridge, "MDCACHE_DENTRY_CLEANUP", &frp);
	if (code != 0) {
		LogMajor(COMPONENT_CACHE_INODE_LRU,
			 "Unable to initialize MDCACHE dentry cleanup fridge, error code %d.",
			 code);
		return posix2fsal_status(code);
	}

	/* Submit the cleanup function */
	code = fridgethr_submit(dentry_cleanup_fridge, mdcache_async_dentry_cleanup, NULL);
	if (code != 0) {
		LogMajor(COMPONENT_CACHE_INODE_LRU,
			 "Unable to start MDCACHE dentry cleanup thread, error code %d.", code);
		fridgethr_destroy(dentry_cleanup_fridge);
		dentry_cleanup_fridge = NULL;
		return posix2fsal_status(code);
	}

	LogInfo(COMPONENT_CACHE_INODE_LRU,
		"MDCACHE async dentry deletion initialized successfully");

	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

/**
 * @brief Shutdown async dentry deletion subsystem
 */
void mdcache_async_dentry_shutdown(void)
{
	if (!mdcache_param.async_dentry_enabled || !dentry_cleanup_fridge) {
		return;
	}

	LogInfo(COMPONENT_CACHE_INODE_LRU,
		"Shutting down MDCACHE async dentry deletion");

	/* Destroy the cleanup thread */
	fridgethr_destroy(dentry_cleanup_fridge);
	dentry_cleanup_fridge = NULL;

	/* Clean up remaining queue entries */
	dentry_del_q_remove_all();

	/* Destroy the mutex */
	PTHREAD_MUTEX_destroy(&dentry_del_q_lane.mtx);

	LogInfo(COMPONENT_CACHE_INODE_LRU,
		"MDCACHE async dentry deletion shutdown completed");
}

/** @} */
